// src/lib/auth-debug.ts
'use client';

/**
 * Debug utilities for authentication issues
 */

export interface AuthDebugInfo {
  sessionExists: boolean;
  sessionValid: boolean;
  sessionExpiry: string | null;
  supabaseSession: boolean;
  currentPath: string;
  timestamp: string;
}

/**
 * Get comprehensive auth debug information
 */
export async function getAuthDebugInfo(): Promise<AuthDebugInfo> {
  const debugInfo: AuthDebugInfo = {
    sessionExists: false,
    sessionValid: false,
    sessionExpiry: null,
    supabaseSession: false,
    currentPath: typeof window !== 'undefined' ? window.location.pathname : '',
    timestamp: new Date().toISOString(),
  };

  try {
    // Check session cookie
    if (typeof document !== 'undefined') {
      const sessionCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('session='));
      
      debugInfo.sessionExists = !!sessionCookie;
    }

    // Check session validity
    try {
      const { verifySession } = await import('./session');
      const sessionUser = await verifySession();
      debugInfo.sessionValid = !!sessionUser;
      
      if (sessionUser) {
        // Get session expiry info
        const { getSession } = await import('./session');
        const session = await getSession();
        debugInfo.sessionExpiry = session?.expiresAt?.toString() || null;
      }
    } catch (error) {
      console.error('Session verification failed in debug:', error);
    }

    // Check Supabase session
    try {
      const { getSession } = await import('../utils/supabase/auth');
      const { session } = await getSession();
      debugInfo.supabaseSession = !!session;
    } catch (error) {
      console.error('Supabase session check failed in debug:', error);
    }

  } catch (error) {
    console.error('Auth debug info collection failed:', error);
  }

  return debugInfo;
}

/**
 * Log auth debug information to console
 */
export async function logAuthDebugInfo(context: string = 'Unknown'): Promise<void> {
  if (process.env.NODE_ENV === 'development') {
    const debugInfo = await getAuthDebugInfo();
    console.group(`🔐 Auth Debug - ${context}`);
    console.table(debugInfo);
    console.groupEnd();
  }
}

/**
 * Check if user should be redirected based on auth state
 */
export async function shouldRedirectToAuth(): Promise<boolean> {
  try {
    const debugInfo = await getAuthDebugInfo();
    
    // Don't redirect if we have a valid session
    if (debugInfo.sessionValid) {
      return false;
    }

    // Don't redirect if we have a Supabase session (might be in process of creating app session)
    if (debugInfo.supabaseSession) {
      return false;
    }

    // Only redirect if we're sure there's no authentication
    return !debugInfo.sessionExists && !debugInfo.supabaseSession;
  } catch (error) {
    console.error('Redirect check failed:', error);
    // Default to not redirecting on errors
    return false;
  }
}

/**
 * Enhanced auth state checker with retry logic
 */
export async function checkAuthStateWithRetry(maxRetries: number = 3): Promise<boolean> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const { verifySession } = await import('../utils/supabase/session');
      const sessionUser = await verifySession();
      
      if (sessionUser) {
        return true;
      }

      // If no session, check Supabase as fallback
      const { getCurrentUser } = await import('../utils/supabase/auth');
      const currentUser = await getCurrentUser();
      
      if (currentUser) {
        // Create session for existing Supabase user
        const { createSession } = await import('../utils/supabase/session');
        await createSession(currentUser);
        return true;
      }

      // If this is not the last attempt, wait before retrying
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    } catch (error) {
      console.error(`Auth check attempt ${attempt} failed:`, error);
      
      // If this is not the last attempt, wait before retrying
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  return false;
}
