// src/utils/supabase/session.ts
'use server';

import { SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';
import { User } from '../auth';

const secretKey = process.env.SESSION_SECRET || 'fallback-secret-key-for-development';
const encodedKey = new TextEncoder().encode(secretKey);

export interface SessionPayload extends Record<string, any> {
  userId: string;
  email: string;
  name: string;
  role: string;
  expiresAt: number;
}

/**
 * Encrypt session data into a JWT token
 */
export async function encrypt(payload: SessionPayload): Promise<string> {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(encodedKey);
}

/**
 * Decrypt JWT token to get session data
 */
export async function decrypt(session: string | undefined = ''): Promise<SessionPayload | null> {
  try {
    // Return null if session is empty or undefined
    if (!session || session.length < 10) {
      return null;
    }
    
    const { payload } = await jwtVerify(session, encodedKey, {
      algorithms: ['HS256'],
    });
    return payload as SessionPayload;
  } catch (error) {
    console.log('Failed to verify session, returning null:', error);
    // Return null instead of throwing error to gracefully handle invalid sessions
    return null;
  }
}

/**
 * Create a new session and store it in cookies
 */
export async function createSession(user: User): Promise<void> {
  const expiresAt = Date.now() + 7 * 24 * 60 * 60 * 1000; // 7 days
  const sessionPayload: SessionPayload = {
    userId: user.email, // Using email as userId for now
    email: user.email,
    name: user.name,
    role: user.role,
    expiresAt,
  };

  const session = await encrypt(sessionPayload);
  const cookieStore = await cookies();

  cookieStore.set('session', session, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    expires: new Date(expiresAt),
    sameSite: 'lax',
    path: '/',
  });
}

/**
 * Get session data from cookies
 */
export async function getSession(): Promise<SessionPayload | null> {
  const cookieStore = await cookies();
  const session = cookieStore.get('session')?.value;
  return await decrypt(session);
}

/**
 * Verify session and return user data
 */
export async function verifySession(): Promise<User | null> {
  try {
    const session = await getSession();

    if (!session) {
      return null;
    }

    // Check if session is expired with grace period
    const expirationTime = new Date(session.expiresAt);
    const currentTime = new Date();
    const gracePeriod = 5 * 60 * 1000; // 5 minutes grace period
    const refreshThreshold = 24 * 60 * 60 * 1000; // Refresh if less than 24 hours remaining

    // Only consider session expired if beyond grace period
    if (currentTime > new Date(expirationTime.getTime() + gracePeriod)) {
      await deleteSession();
      return null;
    }

    // Auto-refresh session if it's close to expiring (within 24 hours)
    const timeUntilExpiry = expirationTime.getTime() - currentTime.getTime();
    if (timeUntilExpiry < refreshThreshold) {
      // Refresh session in background - don't wait for it
      refreshSession().catch(error => {
        console.error('Background session refresh failed:', error);
      });
    }

    return {
      name: session.name,
      email: session.email,
      role: session.role,
      isAuthenticated: true,
    };
  } catch (error) {
    console.error('Session verification error:', error);
    // Don't delete session on verification errors - might be temporary
    return null;
  }
}

/**
 * Update session with new data
 */
export async function updateSession(user: User): Promise<void> {
  const session = await getSession();
  
  if (!session) {
    return;
  }

  const updatedPayload: SessionPayload = {
    ...session,
    name: user.name,
    email: user.email,
    role: user.role,
  };

  const newSession = await encrypt(updatedPayload);
  const cookieStore = await cookies();

  cookieStore.set('session', newSession, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    expires: new Date(session.expiresAt),
    sameSite: 'lax',
    path: '/',
  });
}

/**
 * Refresh session expiration time
 */
export async function refreshSession(): Promise<void> {
  try {
    const session = await getSession();

    if (!session) {
      return;
    }

    // Extend session by another 7 days
    const newExpiresAt = Date.now() + 7 * 24 * 60 * 60 * 1000;
    const updatedPayload: SessionPayload = {
      ...session,
      expiresAt: newExpiresAt,
    };

    const newSession = await encrypt(updatedPayload);
    const cookieStore = await cookies();

    cookieStore.set('session', newSession, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      expires: new Date(newExpiresAt),
      sameSite: 'lax',
      path: '/',
    });
  } catch (error) {
    console.error('Session refresh error:', error);
  }
}

/**
 * Delete session cookie
 */
export async function deleteSession(): Promise<void> {
  try {
    const cookieStore = await cookies();
    cookieStore.delete('session');
  } catch (error) {
    console.error('Delete session error:', error);
  }
}
