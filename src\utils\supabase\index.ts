// src/utils/supabase/index.ts
// Centralized exports for all Supabase utilities

// Core client exports
export { supabase } from './client';
export { getSupabaseClient } from './client-dynamic';

// Authentication exports
export * from './auth';

// Session management exports
export * from './session';

// Data Access Layer exports
export * from './dal';

// Type exports
export * from './types';

// Middleware export (for convenience, though it's typically imported directly)
export { default as middleware, config as middlewareConfig } from './middleware';
