// src/app/student-management/records/page.tsx
'use client';

import { AppLayout } from '../../../components/layout';

export default function SchoolRecordsPage() {
  return (
    <AppLayout title="School Records">
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">School Records</h1>
            <p className="text-gray-600 dark:text-gray-400">Manage student academic records and documents</p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="text-center py-12">
              <div className="mx-auto h-24 w-24 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mb-4">
                <svg className="h-12 w-12 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">School Records Management</h3>
              <p className="text-gray-500 dark:text-gray-400">This page will manage student academic records and official documents.</p>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
