// src/app/student-management/enroll/page.tsx
'use client';

import { AppLayout } from '../../../components/layout';

export default function EnrollStudentPage() {
  return (
    <AppLayout title="Enroll Student">
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Enroll Student</h1>
            <p className="text-gray-600 dark:text-gray-400">Add new students to the system</p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="text-center py-12">
              <div className="mx-auto h-24 w-24 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4">
                <svg className="h-12 w-12 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Student Enrollment</h3>
              <p className="text-gray-500 dark:text-gray-400">This page will contain the student enrollment form.</p>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
